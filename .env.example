# ملف متغيرات البيئة - Environment Variables
# انسخ هذا الملف إلى .env وأدخل القيم الصحيحة

# إعدادات التطبيق الأساسية
VITE_APP_NAME="المساعد الطبي الذكي"
VITE_APP_VERSION="2.0.0"
VITE_APP_DESCRIPTION="نظام ذكي متكامل للاستشارات الطبية وتحليل الأعراض"

# إعدادات API
VITE_API_URL="http://localhost:5000"
VITE_API_TIMEOUT=30000
VITE_API_RETRIES=3

# إعدادات Google Gemini AI
GOOGLE_GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_MODEL="gemini-2.5-flash-preview-05-20"

# إعدادات الأمان
FLASK_SECRET_KEY="your_secret_key_here"
FLASK_ENV="development"
FLASK_DEBUG=true

# إعدادات قاعدة البيانات (اختياري)
DATABASE_URL="sqlite:///medical_assistant.db"

# إعدادات التسجيل
LOG_LEVEL="INFO"
LOG_FILE="logs/medical_assistant.log"

# إعدادات CORS
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"

# إعدادات الذاكرة والتخزين
MEMORY_FILE="data/memory.json"
HISTORY_FILE="data/history.json"
PATIENT_PROFILES_FILE="data/patient_profiles.json"

# إعدادات الحدود
MAX_MESSAGE_LENGTH=2000
MAX_HISTORY_ITEMS=100
MAX_FILE_SIZE=5242880
SESSION_TIMEOUT=1800000

# إعدادات الإشعارات (اختياري)
ENABLE_NOTIFICATIONS=true
NOTIFICATION_SOUND=true
DESKTOP_NOTIFICATIONS=true

# إعدادات التحليلات (اختياري)
ENABLE_ANALYTICS=false
ANALYTICS_ID=""

# إعدادات التطوير
VITE_DEV_TOOLS=true
VITE_HOT_RELOAD=true
VITE_SOURCE_MAPS=true

# إعدادات الإنتاج
VITE_BUILD_OPTIMIZATION=true
VITE_MINIFY=true
VITE_COMPRESSION=true
