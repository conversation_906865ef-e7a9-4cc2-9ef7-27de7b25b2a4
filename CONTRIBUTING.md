# 🤝 دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير المساعد الطبي الذكي! هذا الدليل سيساعدكم على البدء.

## 📋 جدول المحتويات

- [🚀 البدء السريع](#-البدء-السريع)
- [🔧 إعداد البيئة التطويرية](#-إعداد-البيئة-التطويرية)
- [📝 معايير الكود](#-معايير-الكود)
- [🧪 الاختبارات](#-الاختبارات)
- [📤 إرسال المساهمات](#-إرسال-المساهمات)
- [🐛 الإبلاغ عن الأخطاء](#-الإبلاغ-عن-الأخطاء)

## 🚀 البدء السريع

### 1. Fork المشروع
```bash
# انقر على زر Fork في GitHub
# ثم استنسخ المشروع محلياً
git clone https://github.com/YOUR_USERNAME/smart-medical-assistant.git
cd smart-medical-assistant
```

### 2. إنشاء فرع جديد
```bash
git checkout -b feature/your-feature-name
# أو
git checkout -b fix/your-bug-fix
```

### 3. تثبيت المتطلبات
```bash
# Frontend
npm install

# Backend
cd backend
pip install -r requirements.txt
```

## 🔧 إعداد البيئة التطويرية

### متطلبات النظام
- **Node.js** 16.0.0 أو أحدث
- **Python** 3.8 أو أحدث
- **Git** للتحكم في الإصدارات

### إعداد متغيرات البيئة
```bash
# انسخ ملف البيئة النموذجي
cp .env.example .env

# أدخل القيم المطلوبة
# خاصة GOOGLE_GEMINI_API_KEY للذكاء الاصطناعي
```

### تشغيل البيئة التطويرية
```bash
# تشغيل Frontend
npm run dev

# تشغيل Backend (في terminal منفصل)
python backend/simple_app.py
```

## 📝 معايير الكود

### JavaScript/React
- استخدم **ES6+** syntax
- اتبع **React Hooks** patterns
- استخدم **functional components**
- اكتب **JSDoc comments** للوظائف المعقدة

```javascript
/**
 * تحليل الأعراض الطبية
 * @param {string} symptoms - نص الأعراض
 * @returns {Promise<Object>} نتيجة التحليل
 */
const analyzeSymptoms = async (symptoms) => {
  // implementation
};
```

### Python/Flask
- اتبع **PEP 8** style guide
- استخدم **type hints** عند الإمكان
- اكتب **docstrings** للوظائف

```python
def analyze_symptoms(symptoms: str) -> dict:
    """
    تحليل الأعراض الطبية
    
    Args:
        symptoms (str): نص الأعراض
        
    Returns:
        dict: نتيجة التحليل
    """
    pass
```

### CSS
- استخدم **CSS Custom Properties** للمتغيرات
- اتبع **BEM methodology** للتسمية
- اكتب **mobile-first** responsive design

```css
.medical-card {
  /* Base styles */
}

.medical-card__header {
  /* Header styles */
}

.medical-card--emergency {
  /* Emergency variant */
}
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
# اختبارات Frontend
npm test

# اختبارات مع UI
npm run test:ui

# تقرير التغطية
npm run test:coverage
```

### كتابة اختبارات جديدة
```javascript
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';

describe('ComponentName', () => {
  it('should render correctly', () => {
    render(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

## 📤 إرسال المساهمات

### 1. تأكد من جودة الكود
```bash
# فحص الكود
npm run lint

# تنسيق الكود
npm run format

# تشغيل الاختبارات
npm test
```

### 2. Commit Messages
استخدم **Conventional Commits** format:

```bash
# للميزات الجديدة
git commit -m "feat: إضافة تحليل الأعراض المتقدم"

# لإصلاح الأخطاء
git commit -m "fix: إصلاح مشكلة في تفسير التحاليل"

# للتحسينات
git commit -m "perf: تحسين سرعة استجابة API"

# للوثائق
git commit -m "docs: تحديث دليل المساهمة"
```

### 3. إرسال Pull Request
1. ادفع التغييرات إلى فرعك
2. افتح Pull Request في GitHub
3. اكتب وصف واضح للتغييرات
4. اربط أي Issues ذات صلة

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ
- تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
- جرب إعادة إنتاج الخطأ
- اجمع معلومات النظام

### معلومات مطلوبة
```markdown
**وصف الخطأ:**
وصف واضح ومختصر للخطأ

**خطوات إعادة الإنتاج:**
1. اذهب إلى '...'
2. انقر على '...'
3. مرر إلى '...'
4. شاهد الخطأ

**السلوك المتوقع:**
وصف ما كان يجب أن يحدث

**لقطات الشاشة:**
إذا كان ذلك مناسباً، أضف لقطات شاشة

**معلومات النظام:**
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- Version: [e.g. 2.0.0]
```

## 🎯 أنواع المساهمات المرحب بها

- 🐛 **إصلاح الأخطاء**
- ✨ **ميزات جديدة**
- 📚 **تحسين الوثائق**
- 🎨 **تحسين UI/UX**
- ⚡ **تحسين الأداء**
- 🧪 **إضافة اختبارات**
- 🌍 **ترجمات جديدة**
- ♿ **تحسين إمكانية الوصول**

## 📞 التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للأسئلة العامة
- **Email**: للمسائل الحساسة

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع (MIT).

---

**شكراً لمساهمتكم في جعل الرعاية الصحية أكثر إتاحة للجميع! 🙏**
