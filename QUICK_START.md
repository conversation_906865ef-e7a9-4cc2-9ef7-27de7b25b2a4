# 🚀 تشغيل سريع | Quick Start

## 📋 المتطلبات | Requirements

- Node.js (v16 أو أحدث)
- Python (v3.8 أو أحدث)
- npm أو yarn

## ⚡ تشغيل سريع | Quick Run

### الطريقة الأولى: تشغيل تلقائي
```bash
# على Windows
start.bat

# على Mac/Linux
chmod +x start.sh && ./start.sh
```

### الطريقة الثانية: تشغيل يدوي

#### 1. Frontend
```bash
npm install
npm run dev
```

#### 2. Backend (في terminal منفصل)
```bash
cd backend
pip install -r requirements.txt
python app.py
```

## 🌐 الوصول للتطبيق

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000

## 🔧 إعداد Google Gemini API

1. احصل على API Key من [Google AI Studio](https://makersuite.google.com/app/apikey)
2. استبدل `YOUR_API_KEY` في ملف `backend/app.py` بالمفتاح الخاص بك

## 📱 الميزات المتاحة

- ✅ شات طبي ذكي
- ✅ دليل الإسعافات الأولية
- ✅ حفظ تاريخ المحادثات
- ✅ واجهة عربية/إنجليزية
- ✅ تصميم متجاوب

## 🆘 حل المشاكل الشائعة

### مشكلة في تشغيل npm
```bash
# تفعيل تشغيل السكريبت على Windows
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### مشكلة في Python packages
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المكتبات
pip install -r backend/requirements.txt
```

### مشكلة في المنفذ مستخدم
- Frontend سيجد منفذ متاح تلقائياً
- Backend يمكن تغيير المنفذ في `app.py`

## 📞 الدعم

لأي مشاكل أو استفسارات، يرجى فتح Issue في GitHub.
