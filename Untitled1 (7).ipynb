{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "BW2Dvy6kkZua"}, "outputs": [], "source": ["pip install google-genai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RobjIcCUkZxl"}, "outputs": [], "source": ["!pip install gTTS playsound==1.2.2\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "edCmDBIekZ0i"}, "outputs": [], "source": ["!pip install PyMuPDF\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zkMN0SgKkhZF", "outputId": "78855b58-0f22-4162-a490-caf4e62e7352"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "🧠 آخر مرة قلتلي: “أعمل إيه لو حد اغمى عليه؟”\n", "هل اتحسّن الوضع بعد النصايح؟ ولا حصل جديد؟\n"]}], "source": ["import os\n", "import json\n", "from google import genai\n", "from google.genai import types\n", "\n", "MEMORY_FILE = \"memory.json\"\n", "HISTORY_FILE = \"history.json\"\n", "\n", "emotional_triggers = [\n", "    \"مرعوب\", \"خايف\", \"قلقان\", \"متو<PERSON>ر\", \"هموت\", \"بحس بضيق\", \"زعلان\", \"حزين\", \"مخنوق\", \"مجنون\", \"حاسس بضيق\"\n", "]\n", "\n", "# قاموس الإسعافات الأولية مع تفاصيل إضافية وشروحات مبسطة\n", "first_aid_guides = {\n", "    \"حروق\": \"🔥 في حالة الحروق:\\n1. بعدّي المياه الباردة على مكان الحرق لمدة 10 دقايق.\\n2. متحطش ثلج أو معجون أسنان.\\n3. لو الحرق شديد أو فيه فقاعات، لازم تروح طوارئ.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"غرق\": \"🌊 في حالة الغرق:\\n1. طلع الشخص من المية فورًا.\\n2. لو مش بيتنفس، ابتدي **الإنعاش القلبي الرئوي (CPR)**. (لو عايز تعرف إزاي، ممكن تسألني: 'إزاي أعمل CPR؟')\\n3. كلم الإسعاف فورًا.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"إغماء\": \"😵 في حالة الإغماء:\\n1. مدد الشخص على الأرض.\\n2. ارفع رجله شوية.\\n3. لو ما فاقش خلال دقيقة، اتصل بالإسعاف.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"اختناق\": \"😮‍💨 في حالة الاختناق:\\n1. اسأل الشخص لو يقدر يتكلم أو يسعل.\\n2. لو مش قادر، ابتدي **مناورة هيمليخ**. (لو عايز تعرف إزاي، ممكن تسألني: 'إزاي أعمل مناورة هيمليخ؟')\\n3. لو فقد الوعي، ابتدي **الإنعاش القلبي الرئوي (CPR)** واتصل بالإسعاف.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"جروح\": \"🩸 في حالة الجروح:\\n1. نظّف الجرح بمياه جارية.\\n2. حط شاش معقم واضغط عليه.\\n3. لو الجرح عميق أو بينزف كتير، لازم تروح طوارئ.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"تسمم\": \"☠️ في حالة التسمم:\\n1. متحاولش تخلي الشخص يرجع.\\n2. اتصل بمركز السموم فورًا.\\n3. اعرف نوع المادة اللي اتسمم منها لو أمكن.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"صرع\": \"⚡ في حالة نوبة صرع:\\n1. ابعد أي حاجة ممكن تأذيه.\\n2. متقيدهوش، وسيبه لحد ما النوبة تخلص.\\n3. لو النوبة استمرت أكتر من ٥ دقايق، اتصل بالإسعاف.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"ضربات الرأس\": \"🧠 في حالة ضربة رأس:\\n1. راقب الشخص لو فيه قيء، فقدان وعي، أو صداع شديد.\\n2. لو فيه أي من دول، لازم يروح طوارئ.\\n3. متديهش أدوية بدون استشارة طبيب.\\n\\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\",\n", "    \"cpr\": \"❤️ إزاي تعمل الإنعاش القلبي الرئوي (CPR):\\n1. تأكد إن المكان آمن، وان الشخص مش بيتنفس أو مش بيستجيب.\\n2. اطلب المساعدة فورًا (اتصل بالإسعاف).\\n3. حط إيد على إيد في نص صدر الشخص.\\n4. اضغط بقوة وسرعة (حوالي 100-120 ضغطة في الدقيقة) بعمق 5-6 سم.\\n5. بعد كل 30 ضغطة، ممكن تعمل نفسين إنقاذ لو مدرب على كده، أو استمر في الضغطات بس.\\n6. استمر لحد ما يوصل الإسعاف أو الشخص يستجيب.\\n\\n⚠️ ده شرح مبسط ومبيغنيش عن التدريب العملي على الـ CPR.\",\n", "    \"مناورة هيمليخ\": \"👐 إزاي تعمل مناورة هيمليخ (لشخص واعي بيختنق):\\n1. اقف ورا الشخص، وحاوط وسطه بإيديك.\\n2. حط قبضة إيدك فوق سرة الشخص بشوية، وإيدك التانية فوقها.\\n3. اضغط بقوة وسرعة للداخل وللأعلى في نفس الوقت، زي ما تكون عايز ترفع الشخص.\\n4. كرر الضغطات دي لحد ما الجسم الغريب يطلع أو الشخص يفقد الوعي.\\n5. لو فقد الوعي، مدد الشخص على الأرض وابتدي CPR.\\n\\n⚠️ ده شرح مبسط ومبيغنيش عن التدريب العملي على الإسعافات الأولية.\"\n", "}\n", "\n", "def handle_first_aid(text):\n", "    text_lower = text.lower()\n", "    for keyword, guide in first_aid_guides.items():\n", "        if keyword in text_lower: # تأكد من البحث في النص المحول لحروف صغيرة\n", "            return guide\n", "    return None\n", "\n", "# باقي الكود كما هو\n", "def is_arabic(text):\n", "    for ch in text:\n", "        if '\\u0600' <= ch <= '\\u06FF' or '\\u0750' <= ch <= '\\u077F':\n", "            return True\n", "    return False\n", "\n", "def print_response(arabic_text, english_text, user_text):\n", "    if is_arabic(user_text):\n", "        print(arabic_text)\n", "    else:\n", "        print(english_text)\n", "\n", "def save_last_question(question):\n", "    with open(MEMORY_FILE, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump({\"last_question\": question}, f, ensure_ascii=False)\n", "\n", "def load_last_question():\n", "    if os.path.exists(MEMORY_FILE):\n", "        with open(MEMORY_FILE, \"r\", encoding=\"utf-8\") as f:\n", "            data = json.load(f)\n", "            return data.get(\"last_question\", \"\")\n", "    return \"\"\n", "\n", "def log_conversation(question, answer):\n", "    data = []\n", "    if os.path.exists(HISTORY_FILE):\n", "        with open(HISTORY_FILE, \"r\", encoding=\"utf-8\") as f:\n", "            data = json.load(f)\n", "    data.append({\"q\": question, \"a\": answer})\n", "    with open(HISTORY_FILE, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(data, f, ensure_ascii=False, indent=2)\n", "\n", "def search_history(keyword):\n", "    if not os.path.exists(HISTORY_FILE):\n", "        return \"❌ مفيش محادثات قديمة.\\n\\n❌ No previous conversations found.\"\n", "    with open(HISTORY_FILE, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "    results = [f\"❓ {item['q']}\\n🤖 {item['a']}\" for item in data if keyword in item[\"q\"]]\n", "    if results:\n", "        return \"\\n\\n\".join(results)\n", "    else:\n", "        return \"❌ ملقتش حاجة بالسؤال ده.\\n\\n❌ No results found for this query.\"\n", "\n", "def is_emotional(text):\n", "    text_lower = text.lower()\n", "    return any(trigger in text_lower for trigger in emotional_triggers)\n", "\n", "def filter_medical_question(text):\n", "    non_medical_keywords = [\"سياسة\", \"رياضة\", \"فيلم\", \"موسيقى\", \"برمجة\", \"تعليم\", \"ترفيه\"]\n", "    text_lower = text.lower()\n", "    if any(word in text_lower for word in non_medical_keywords):\n", "        return False\n", "    return True\n", "\n", "def summarize_conversation(history):\n", "    symptoms = []\n", "    diagnostics = []\n", "    advice = []\n", "    for item in history:\n", "        q = item[\"q\"].lower()\n", "        a = item[\"a\"]\n", "        if any(word in q for word in [\"ألم\", \"صداع\", \"حمى\", \"سعال\", \"دوخة\", \"إعياء\"]):\n", "            symptoms.append(q)\n", "        if any(word in a.lower() for word in [\"ممكن يكون\", \"يرجح\", \"التشخيص المحتمل\"]):\n", "            diagnostics.append(a)\n", "        if any(word in a.lower() for word in [\"ينصح\", \"تناول\", \"دواء\", \"راحة\", \"شرب ماء\"]):\n", "            advice.append(a)\n", "\n", "    summary = \"📋 ملخص الحالة:\\n\"\n", "    if symptoms:\n", "        summary += \"- الأعراض الأساسية:\\n\" + \"\\n\".join(f\"  • {s}\" for s in symptoms) + \"\\n\"\n", "    if diagnostics:\n", "        summary += \"- الترجيحات التشخيصية:\\n\" + \"\\n\".join(f\"  • {d}\" for d in diagnostics) + \"\\n\"\n", "    if advice:\n", "        summary += \"- النصائح أو الأدوية المقترحة:\\n\" + \"\\n\".join(f\"  • {ad}\" for ad in advice) + \"\\n\"\n", "    if not symptoms and not diagnostics and not advice:\n", "        summary += \"لم يتم تجميع معلومات كافية للملخص.\\n\"\n", "    return summary\n", "\n", "def main():\n", "    client = genai.Client(api_key=\"AIzaSyBPs9sNSu0-4Z33TUEvHrVF6Mx1CKLj9zQ\")\n", "    model = \"gemini-2.5-flash-preview-05-20\"\n", "\n", "    generate_content_config = types.GenerateContentConfig(\n", "        safety_settings=[\n", "            types.SafetySetting(category=\"HARM_CATEGORY_HARASSMENT\", threshold=\"BLOCK_ONLY_HIGH\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_HATE_SPEECH\", threshold=\"BLOCK_ONLY_HIGH\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_SEXUALLY_EXPLICIT\", threshold=\"BLOCK_ONLY_HIGH\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_DANGEROUS_CONTENT\", threshold=\"BLOCK_ONLY_HIGH\"),\n", "        ],\n", "        response_mime_type=\"text/plain\",\n", "        system_instruction=[\n", "            types.Part.from_text(text=\"\"\"\n", "You are a helpful and knowledgeable medical assistant. Your role is to:\n", "- Provide clear, accurate, and concise medical information.\n", "- Reply in Arabic if the user writes in Arabic, especially Egyptian dialect.\n", "- Always include this disclaimer at the end: ⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.\n", "\n", "✅ If the user asks for a diet plan, ask about their health goal (e.g., weight loss, diabetes, anemia), dietary preference (e.g., vegetarian), and suggest a simple meal plan (breakfast, lunch, dinner). Make it affordable and relevant to the region.\n", "\n", "✅ If the user shares lab results (e.g., CBC, blood sugar), explain the values in simple terms. Mention if it's high/low, what it might mean, and recommend seeing a doctor for full interpretation.\n", "\n", "✅ Be always friendly, respectful, and keep things easy to understand.\n", "\"\"\"),\n", "        ],\n", "    )\n", "\n", "    last_question = load_last_question()\n", "    if last_question:\n", "        print_response(\n", "            f\"\\n🧠 آخر مرة قلتلي: “{last_question}”\\nهل اتحسّن الوضع بعد النصايح؟ ولا حصل جديد؟\",\n", "            f\"\\n🧠 Last time you told me: “{last_question}”\\nDid your condition improve after the advice? Or is there any update?\",\n", "            last_question\n", "        )\n", "\n", "    conversation_history = []\n", "    while True:\n", "        user_input = input(\"\\n🩺 اسأل سؤال طبي (أو اكتب 'خروج' لإنهاء): \").strip()\n", "        if user_input.lower() in [\"خروج\", \"exit\", \"quit\"]:\n", "            if conversation_history:\n", "                print_response(\n", "                    \"\\n🤖 ملخص المحادثة الطبية:\",\n", "                    \"\\n🤖 Medical conversation summary:\",\n", "                    user_input\n", "                )\n", "                summary = summarize_conversation(conversation_history)\n", "                print(summary)\n", "            print_response(\n", "                \"👋 تمام، لو احتجتني تاني أنا موجود.\",\n", "                \"👋 Okay, if you need me again, I'm here.\",\n", "                user_input\n", "            )\n", "            break\n", "\n", "        if not filter_medical_question(user_input):\n", "            print_response(\n", "                \"⚠️ عذرًا، السؤال مش طبي أو مش واضح، ممكن تسألني حاجة طبية؟\",\n", "                \"⚠️ Sorry, the question is not medical or not clear. Could you please ask a medical question?\",\n", "                user_input\n", "            )\n", "            continue\n", "\n", "        if is_emotional(user_input):\n", "            print_response(\n", "                \"🤗 باين عليك متوتر أو قلقان، خليك هادي وأنا هنا أساعدك.\",\n", "                \"🤗 You seem anxious or stressed, stay calm, I'm here to help.\",\n", "                user_input\n", "            )\n", "\n", "        if \"كنت سألتك عن\" in user_input:\n", "            keyword = user_input.replace(\"كنت سألتك عن\", \"\").strip()\n", "            results = search_history(keyword)\n", "            print(results)\n", "            continue\n", "\n", "        if any(word in user_input for word in [\"تحليل\", \"نتيجة\", \"التحاليل\"]):\n", "            print_response(\n", "                \"📊 لو عندك نتايج تحليل (زي: الهيموجلوبين 11.5، السكر 120)، ابعتها زي ما هي علشان أشرحها.\",\n", "                \"📊 If you have lab results (e.g., hemoglobin 11.5, sugar 120), send them as is for explanation.\",\n", "                user_input\n", "            )\n", "\n", "        # 🔍 فحص لو السؤال يخص الإسعافات الأولية\n", "        first_aid_response = handle_first_aid(user_input)\n", "        if first_aid_response:\n", "            print(first_aid_response)\n", "            log_conversation(user_input, first_aid_response)\n", "            conversation_history.append({\"q\": user_input, \"a\": first_aid_response})\n", "            print(\"\\n\" + \"-\"*60)\n", "            continue\n", "\n", "        save_last_question(user_input)\n", "\n", "        contents = [\n", "            types.Content(\n", "                role=\"user\",\n", "                parts=[\n", "                    types.Part.from_text(text=user_input),\n", "                ],\n", "            ),\n", "        ]\n", "\n", "        print_response(\n", "            \"🤖 المساعد الطبي بيجاوب...\\n\",\n", "            \"🤖 Medical assistant is answering...\\n\",\n", "            user_input\n", "        )\n", "        answer = \"\"\n", "        for chunk in client.models.generate_content_stream(\n", "            model=model,\n", "            contents=contents,\n", "            config=generate_content_config,\n", "        ):\n", "            print(chunk.text, end=\"\")\n", "            answer += chunk.text\n", "\n", "        log_conversation(user_input, answer)\n", "        conversation_history.append({\"q\": user_input, \"a\": answer})\n", "        print(\"\\n\" + \"-\"*60)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}