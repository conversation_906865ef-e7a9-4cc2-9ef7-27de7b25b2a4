{"name": "smart-medical-assistant", "private": true, "version": "2.0.0", "description": "Advanced AI-powered medical assistant with comprehensive health analysis", "type": "module", "keywords": ["medical", "ai", "healthcare", "assistant", "diagnosis", "symptoms"], "author": "Medical AI Team", "license": "MIT", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2", "lucide-react": "^0.300.0", "react-router-dom": "^6.20.1", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.0", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "vite-bundle-analyzer": "^0.7.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}