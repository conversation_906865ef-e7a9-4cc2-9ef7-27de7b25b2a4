#!/usr/bin/env python3
"""
تشغيل سريع للـ Backend
Quick Backend Runner
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات"""
    print("🔧 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False
    return True

def run_backend():
    """تشغيل الخادم"""
    print("🚀 تشغيل الخادم...")
    try:
        os.chdir("backend")
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    print("🩺 المساعد الطبي الذكي - Backend")
    print("=" * 40)
    
    if install_requirements():
        run_backend()
    else:
        print("❌ لا يمكن تشغيل الخادم بدون تثبيت المتطلبات")
