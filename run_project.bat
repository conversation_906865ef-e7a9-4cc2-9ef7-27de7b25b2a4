@echo off
title المساعد الطبي الذكي المتطور v2.0
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🩺 المساعد الطبي الذكي المتطور v2.0                ║
echo ║                   Advanced Smart Medical Assistant                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 بدء تشغيل النظام الطبي المتكامل...
echo ⚡ Initializing Integrated Medical System...
echo.

echo 📋 فحص المتطلبات الأساسية...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

:: فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo 🔗 https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: فحص Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً
    echo 🔗 https://python.org/
    pause
    exit /b 1
)
echo ✅ Python متوفر

echo.
echo 📦 تثبيت المكتبات المطلوبة...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo 🔧 تثبيت مكتبات Frontend...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت مكتبات Frontend
    pause
    exit /b 1
)
echo ✅ تم تثبيت مكتبات Frontend بنجاح

echo.
echo 🐍 تثبيت مكتبات Python...
pip install flask flask-cors google-genai
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: بعض مكتبات Python قد لا تكون متاحة
    echo 💡 سيتم استخدام النسخة المبسطة من Backend
)
echo ✅ تم تثبيت مكتبات Python

echo.
echo 🚀 تشغيل الخوادم...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo 🌐 تشغيل Frontend Server...
start "🎨 Frontend - React App" cmd /k "echo 🎨 Frontend Server && echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ && npm run dev"

timeout /t 3 >nul

echo 🐍 تشغيل Backend Server...
start "🔧 Backend - Python API" cmd /k "echo 🔧 Backend Server && echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ && python backend\simple_app.py"

echo.
echo ⏳ انتظار تشغيل الخوادم...
timeout /t 8 >nul

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        ✅ تم تشغيل النظام بنجاح!                        ║
echo ║                      🎉 System Started Successfully!                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 الروابط المتاحة:
echo ┌─────────────────────────────────────────────────────────────┐
echo │  🎨 Frontend:     http://localhost:3000                     │
echo │  🔧 Backend API:  http://localhost:5000                     │
echo │  📊 Health Check: http://localhost:5000/api/health-tips     │
echo └─────────────────────────────────────────────────────────────┘
echo.
echo 🎯 الميزات المتاحة:
echo   ✨ استشارة طبية ذكية مدعومة بالذكاء الاصطناعي
echo   🔍 تحليل الأعراض المتقدم مع كشف الطوارئ
echo   📊 تفسير التحاليل الطبية بطريقة مبسطة
echo   🚨 دليل الإسعافات الأولية التفاعلي
echo   💊 نصائح صحية يومية مخصصة
echo   📱 واجهة متجاوبة تعمل على جميع الأجهزة
echo.
echo 💡 نصائح مهمة:
echo   • اتركي نوافذ الخوادم مفتوحة للحفاظ على عمل النظام
echo   • استخدم Ctrl+C لإيقاف أي خادم
echo   • في حالة وجود مشاكل، راجع ملف README.md
echo.
echo 🔗 للمزيد من المعلومات: https://github.com/your-repo
echo.
echo اضغط أي مفتاح للخروج من هذه النافذة...
pause >nul
