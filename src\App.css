/* التطبيق الرئيسي */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  font-family: 'Cairo', sans-serif;
}

/* رأس التطبيق */
.app-header {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255,255,255,0.2);
  padding: 20px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.language-toggle {
  background: rgba(255,255,255,0.2);
  border: 2px solid rgba(255,255,255,0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  align-self: flex-end;
}

.language-toggle:hover {
  background: rgba(255,255,255,0.3);
  border-color: rgba(255,255,255,0.5);
}

.header-title {
  text-align: center;
  color: white;
}

.header-title h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-title p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

.daily-tip {
  background: rgba(255,255,255,0.2);
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.3);
  max-width: 600px;
  text-align: center;
}

.daily-tip span {
  font-size: 14px;
  color: white;
  line-height: 1.4;
}

/* شريط التبويبات */
.tabs-container {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.tabs {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  padding: 5px;
  gap: 5px;
}

.tab {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  min-height: 44px;
}

.tab:hover {
  background: rgba(255,255,255,0.1);
}

.tab.active {
  background: white;
  color: #333;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* المحتوى الرئيسي */
.main-content {
  flex: 1;
  padding: 30px 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* مكونات الميزات */
.feature-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  padding: 30px;
  height: 600px;
  overflow-y: auto;
}

.feature-container.emergency {
  border-left: 4px solid #dc3545;
}

.feature-header {
  text-align: center;
  margin-bottom: 30px;
}

.feature-header h3 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #333;
  margin: 0 0 10px 0;
  font-size: 1.5rem;
}

.feature-header p {
  color: #666;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-textarea {
  width: 100%;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  font-size: 16px;
  font-family: 'Cairo', sans-serif;
  resize: vertical;
  min-height: 150px;
  outline: none;
  transition: border-color 0.3s;
  line-height: 1.6;
}

.feature-textarea:focus {
  border-color: #4facfe;
}

.feature-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: transform 0.2s;
  align-self: center;
}

.feature-button:hover {
  transform: translateY(-2px);
}

/* مركز الطوارئ */
.emergency-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.emergency-numbers h4,
.first-aid-quick h4 {
  color: #333;
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  text-align: center;
}

.numbers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.emergency-number {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.2s;
  border: 2px solid transparent;
}

.emergency-number:hover {
  transform: translateY(-2px);
  border-color: #dc3545;
}

.emergency-number span {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.first-aid-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.first-aid-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.first-aid-item:hover {
  background: #e9ecef;
  border-color: #4facfe;
  transform: translateY(-2px);
}

.aid-icon {
  font-size: 32px;
}

.aid-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

/* تذييل التطبيق */
.app-footer {
  background: rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255,255,255,0.2);
  padding: 20px;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  color: white;
}

.footer-content p {
  margin: 0 0 10px 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.5;
}

.footer-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 12px;
  opacity: 0.7;
}

/* تجاوب */
@media (max-width: 768px) {
  .app-header {
    padding: 15px;
  }
  
  .header-title h1 {
    font-size: 2rem;
  }
  
  .header-title p {
    font-size: 1rem;
  }
  
  .tabs-container {
    padding: 15px;
  }
  
  .tabs {
    flex-wrap: wrap;
  }
  
  .tab {
    font-size: 12px;
    padding: 10px 15px;
    min-width: 120px;
  }
  
  .main-content {
    padding: 20px 15px;
  }
  
  .feature-container {
    padding: 20px;
    height: auto;
    min-height: 400px;
  }
  
  .feature-header h3 {
    font-size: 1.3rem;
  }
  
  .feature-textarea {
    min-height: 120px;
    padding: 15px;
  }
  
  .numbers-grid,
  .first-aid-grid {
    grid-template-columns: 1fr;
  }
  
  .daily-tip {
    padding: 10px 15px;
  }
  
  .daily-tip span {
    font-size: 13px;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 5px;
  }
}

/* وضع الظلام */
@media (prefers-color-scheme: dark) {
  .feature-container {
    background: #1a202c;
    color: #e2e8f0;
  }
  
  .feature-header h3 {
    color: #e2e8f0;
  }
  
  .feature-header p {
    color: #a0aec0;
  }
  
  .feature-textarea {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .feature-textarea:focus {
    border-color: #4facfe;
  }
  
  .emergency-number,
  .first-aid-item {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .emergency-number span,
  .aid-title {
    color: #e2e8f0;
  }
  
  .emergency-numbers h4,
  .first-aid-quick h4 {
    color: #e2e8f0;
  }
}
