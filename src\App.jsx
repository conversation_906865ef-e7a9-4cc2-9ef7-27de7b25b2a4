import React, { useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { 
  Globe, Activity, Stethoscope, TestTube, AlertTriangle, 
  Star, Phone, Shield, Zap
} from 'lucide-react';
import ErrorBoundary from './components/common/ErrorBoundary.jsx';
import ChatContainer from './components/chat/ChatContainer.jsx';
import { useAppStore } from './store/useAppStore.js';
import { medicalAPI } from './services/api.js';
import { APP_CONFIG } from './config/constants.js';
import './App.css';

// بيانات الإسعافات الأولية
const firstAidGuides = {
  "حروق": { icon: "🔥", title: "حروق" },
  "إغماء": { icon: "😵", title: "إغماء" },
  "اختناق": { icon: "😮‍💨", title: "اختناق" },
  "جروح": { icon: "🩸", title: "جروح" },
  "CPR": { icon: "❤️", title: "إنعاش قلبي" },
  "صرع": { icon: "⚡", title: "نوبة صرع" }
};

function App() {
  const {
    language,
    currentTab,
    healthTip,
    setLanguage,
    setCurrentTab,
    setHealthTip
  } = useAppStore();

  // تحميل النصائح الصحية عند بدء التطبيق
  useEffect(() => {
    fetchHealthTip();
  }, [setHealthTip]);

  const fetchHealthTip = async () => {
    try {
      const result = await medicalAPI.getHealthTips();
      if (result.success) {
        setHealthTip(result.data.daily_tip);
      }
    } catch (error) {
      console.error('Error fetching health tip:', error);
    }
  };

  // مكونات التبويبات
  const renderTabContent = () => {
    switch (currentTab) {
      case 'chat':
        return <ChatContainer />;
      case 'symptoms':
        return renderSymptomsAnalyzer();
      case 'labs':
        return renderLabsInterpreter();
      case 'emergency':
        return renderEmergencyCenter();
      default:
        return <ChatContainer />;
    }
  };

  const renderSymptomsAnalyzer = () => (
    <div className="feature-container">
      <div className="feature-header">
        <Activity size={24} />
        <h3>محلل الأعراض المتقدم</h3>
        <p>تحليل ذكي شامل للأعراض مع تقييم مستوى الخطورة</p>
      </div>
      <div className="feature-content">
        <textarea
          placeholder="اكتب جميع الأعراض التي تشعر بها بالتفصيل..."
          rows="6"
          className="feature-textarea"
        />
        <button className="feature-button">
          <Activity size={16} />
          تحليل الأعراض
        </button>
      </div>
    </div>
  );

  const renderLabsInterpreter = () => (
    <div className="feature-container">
      <div className="feature-header">
        <TestTube size={24} />
        <h3>مفسر التحاليل الطبية</h3>
        <p>تفسير مبسط ومفهوم لنتائج التحاليل الطبية</p>
      </div>
      <div className="feature-content">
        <textarea
          placeholder="الصق نتائج التحاليل هنا (مثل: هيموجلوبين 12.5، سكر صائم 95)..."
          rows="6"
          className="feature-textarea"
        />
        <button className="feature-button">
          <TestTube size={16} />
          تفسير النتائج
        </button>
      </div>
    </div>
  );

  const renderEmergencyCenter = () => (
    <div className="feature-container emergency">
      <div className="feature-header">
        <AlertTriangle size={24} />
        <h3>مركز الطوارئ</h3>
        <p>إرشادات فورية وأرقام الطوارئ</p>
      </div>
      <div className="emergency-content">
        <div className="emergency-numbers">
          <h4>أرقام الطوارئ:</h4>
          <div className="numbers-grid">
            <div className="emergency-number">
              <Phone size={20} />
              <span>إسعاف: 123</span>
            </div>
            <div className="emergency-number">
              <Shield size={20} />
              <span>شرطة: 122</span>
            </div>
            <div className="emergency-number">
              <Zap size={20} />
              <span>إطفاء: 180</span>
            </div>
          </div>
        </div>
        
        <div className="first-aid-quick">
          <h4>إسعافات أولية سريعة:</h4>
          <div className="first-aid-grid">
            {Object.entries(firstAidGuides).map(([key, guide]) => (
              <div key={key} className="first-aid-item">
                <span className="aid-icon">{guide.icon}</span>
                <span className="aid-title">{guide.title}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <ErrorBoundary>
      <div className="app" dir={language === 'ar' ? 'rtl' : 'ltr'}>
        <Toaster 
          position="top-center"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Cairo, sans-serif'
            }
          }}
        />
        
        {/* رأس التطبيق */}
        <header className="app-header">
          <div className="header-content">
            <button 
              className="language-toggle"
              onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
            >
              <Globe size={16} />
              {language === 'ar' ? 'English' : 'العربية'}
            </button>
            
            <div className="header-title">
              <h1>🩺 {APP_CONFIG.name}</h1>
              <p>{APP_CONFIG.description}</p>
            </div>
            
            {healthTip && (
              <div className="daily-tip">
                <Star size={16} />
                <span>{healthTip}</span>
              </div>
            )}
          </div>
        </header>

        {/* شريط التبويبات */}
        <nav className="tabs-container">
          <div className="tabs">
            <button 
              className={`tab ${currentTab === 'chat' ? 'active' : ''}`}
              onClick={() => setCurrentTab('chat')}
            >
              <Stethoscope size={16} />
              استشارة طبية
            </button>
            <button 
              className={`tab ${currentTab === 'symptoms' ? 'active' : ''}`}
              onClick={() => setCurrentTab('symptoms')}
            >
              <Activity size={16} />
              تحليل الأعراض
            </button>
            <button 
              className={`tab ${currentTab === 'labs' ? 'active' : ''}`}
              onClick={() => setCurrentTab('labs')}
            >
              <TestTube size={16} />
              تفسير التحاليل
            </button>
            <button 
              className={`tab ${currentTab === 'emergency' ? 'active' : ''}`}
              onClick={() => setCurrentTab('emergency')}
            >
              <AlertTriangle size={16} />
              طوارئ
            </button>
          </div>
        </nav>

        {/* المحتوى الرئيسي */}
        <main className="main-content">
          {renderTabContent()}
        </main>

        {/* تذييل التطبيق */}
        <footer className="app-footer">
          <div className="footer-content">
            <p>
              ⚠️ هذا المساعد يقدم معلومات طبية عامة فقط ولا يغني عن استشارة الطبيب المتخصص
            </p>
            <div className="footer-info">
              <span>الإصدار {APP_CONFIG.version}</span>
              <span>•</span>
              <span>مطور بـ ❤️ للمجتمع العربي</span>
            </div>
          </div>
        </footer>
      </div>
    </ErrorBoundary>
  );
}

export default App;
