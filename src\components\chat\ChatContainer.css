.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  overflow: hidden;
}

.chat-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 20px;
  position: relative;
}

.chat-title h3 {
  margin: 0 0 4px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.chat-title p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.detected-symptoms-header {
  margin-top: 15px;
  padding: 12px;
  background: rgba(255,255,255,0.15);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.symptoms-label {
  display: block;
  font-size: 0.85rem;
  margin-bottom: 8px;
  opacity: 0.9;
}

.symptoms-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.symptom-tag {
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(255,255,255,0.3);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
  scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.typing-indicator {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.chat-input {
  padding: 20px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 100%;
}

.input-actions-left,
.input-actions-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn,
.voice-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.3s ease;
}

.action-btn:hover,
.voice-btn:hover {
  background: #e9ecef;
  border-color: #4facfe;
  color: #4facfe;
  transform: translateY(-2px);
}

.voice-btn.recording {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
  animation: pulse 1.5s infinite;
}

.input-wrapper {
  flex: 1;
  position: relative;
  min-width: 0;
}

.message-input {
  width: 100%;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 22px;
  font-size: 16px;
  font-family: 'Cairo', sans-serif;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
  background: white;
  line-height: 1.4;
}

.message-input:focus {
  border-color: #4facfe;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.message-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.message-input::placeholder {
  color: #adb5bd;
}

.input-counter {
  position: absolute;
  bottom: -20px;
  right: 16px;
  font-size: 11px;
  color: #6c757d;
  opacity: 0.7;
}

.send-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.send-btn:disabled {
  background: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* تأثيرات الحركة */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* تجاوب */
@media (max-width: 768px) {
  .chat-container {
    border-radius: 0;
    height: 100vh;
  }
  
  .chat-header {
    padding: 16px;
  }
  
  .chat-title h3 {
    font-size: 1.1rem;
  }
  
  .chat-messages {
    padding: 16px;
  }
  
  .chat-input {
    padding: 16px;
  }
  
  .input-container {
    gap: 8px;
  }
  
  .action-btn,
  .voice-btn,
  .send-btn {
    width: 40px;
    height: 40px;
  }
  
  .message-input {
    font-size: 16px; /* منع التكبير على iOS */
    padding: 10px 14px;
  }
  
  .input-actions-left {
    order: 3;
  }
  
  .input-wrapper {
    order: 2;
  }
  
  .input-actions-right {
    order: 1;
  }
}

/* وضع الظلام */
@media (prefers-color-scheme: dark) {
  .chat-container {
    background: #1a202c;
  }
  
  .chat-messages {
    background: #2d3748;
  }
  
  .chat-input {
    background: #1a202c;
    border-top-color: #4a5568;
  }
  
  .action-btn,
  .voice-btn {
    background: #2d3748;
    border-color: #4a5568;
    color: #a0aec0;
  }
  
  .action-btn:hover,
  .voice-btn:hover {
    background: #4a5568;
    color: #e2e8f0;
  }
  
  .message-input {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .message-input:focus {
    border-color: #4facfe;
  }
  
  .message-input::placeholder {
    color: #718096;
  }
  
  .input-counter {
    color: #a0aec0;
  }
}

/* تحسينات الوصولية */
.action-btn:focus,
.voice-btn:focus,
.send-btn:focus,
.message-input:focus {
  outline: 2px solid #4facfe;
  outline-offset: 2px;
}

/* تأثيرات خاصة */
.chat-container.emergency-mode {
  border: 2px solid #dc3545;
  box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
}

.chat-container.emergency-mode .chat-header {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.chat-container.offline {
  opacity: 0.7;
  pointer-events: none;
}

.chat-container.offline::after {
  content: 'غير متصل';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 1000;
}
