import React, { useState, useRef, useEffect } from 'react';
import { Send, Mic, MicOff, Paperclip, Smile } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import ChatMessage from './ChatMessage.jsx';
import LoadingSpinner from '../common/LoadingSpinner.jsx';
import { useAppStore } from '../../store/useAppStore.js';
import { medicalAPI, retryRequest } from '../../services/api.js';
import { MESSAGE_TYPES, APP_CONFIG } from '../../config/constants.js';
import './ChatContainer.css';

const ChatContainer = () => {
  const {
    messages,
    isLoading,
    detectedSymptoms,
    addMessage,
    setLoading,
    addDetectedSymptoms,
    addToHistory,
    updateStats
  } = useAppStore();

  const [inputValue, setInputValue] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const fileInputRef = useRef(null);

  // التمرير التلقائي لآخر رسالة
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: "smooth",
      block: "end"
    });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // التركيز على حقل الإدخال عند التحميل
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // إرسال الرسالة
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    // التحقق من طول الرسالة
    if (inputValue.length > APP_CONFIG.limits.maxMessageLength) {
      toast.error(`الرسالة طويلة جداً. الحد الأقصى ${APP_CONFIG.limits.maxMessageLength} حرف.`);
      return;
    }

    const userMessage = {
      type: MESSAGE_TYPES.USER,
      content: inputValue.trim(),
      sender: 'user'
    };

    // إضافة رسالة المستخدم
    addMessage(userMessage);
    setInputValue('');
    setLoading(true);

    try {
      // إرسال الرسالة للـ API مع إعادة المحاولة
      const result = await retryRequest(() => 
        medicalAPI.sendMessage(inputValue.trim())
      );

      if (result.success) {
        const botMessage = {
          type: result.data.message_type || MESSAGE_TYPES.BOT,
          content: result.data.response,
          sender: 'bot',
          confidence: result.data.confidence_score,
          isEmergency: result.data.is_emergency,
          detectedSymptoms: result.data.detected_symptoms || []
        };

        addMessage(botMessage);

        // إضافة الأعراض المكتشفة
        if (result.data.detected_symptoms?.length > 0) {
          addDetectedSymptoms(result.data.detected_symptoms);
        }

        // حفظ في التاريخ
        addToHistory({
          question: userMessage.content,
          answer: botMessage.content,
          type: botMessage.type,
          symptoms: result.data.detected_symptoms
        });

        // تحديث الإحصائيات
        updateStats('totalQuestions');
        if (result.data.is_emergency) {
          updateStats('emergenciesDetected');
        }

        // إشعار في حالة الطوارئ
        if (result.data.is_emergency) {
          toast.error('تم اكتشاف حالة طارئة محتملة! يرجى طلب المساعدة الطبية فوراً.', {
            duration: 10000,
            icon: '🚨'
          });
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage = {
        type: MESSAGE_TYPES.ERROR,
        content: `عذراً، حدث خطأ: ${error.message}\nيرجى المحاولة مرة أخرى.`,
        sender: 'system'
      };
      
      addMessage(errorMessage);
      toast.error('فشل في إرسال الرسالة');
    } finally {
      setLoading(false);
    }
  };

  // معالجة الضغط على Enter
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // تسجيل الصوت (قيد التطوير)
  const handleVoiceRecording = () => {
    if (!isRecording) {
      setIsRecording(true);
      toast.success('بدء التسجيل...');
      // TODO: تنفيذ تسجيل الصوت
    } else {
      setIsRecording(false);
      toast.success('انتهى التسجيل');
      // TODO: معالجة الصوت المسجل
    }
  };

  // رفع ملف (قيد التطوير)
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > APP_CONFIG.limits.maxFileSize) {
        toast.error('الملف كبير جداً. الحد الأقصى 5 ميجابايت.');
        return;
      }
      
      toast.success(`تم رفع الملف: ${file.name}`);
      // TODO: معالجة الملف المرفوع
    }
  };

  // نسخ الرسالة
  const handleCopyMessage = (messageId) => {
    toast.success('تم نسخ النص');
  };

  // تقييم الرسالة
  const handleMessageFeedback = (messageId, type) => {
    toast.success(type === 'positive' ? 'شكراً لتقييمك الإيجابي' : 'شكراً لملاحظاتك');
    // TODO: إرسال التقييم للخادم
  };

  return (
    <div className="chat-container">
      {/* رأس الشات */}
      <div className="chat-header">
        <div className="chat-title">
          <h3>💬 استشارة طبية متقدمة</h3>
          <p>مدعوم بالذكاء الاصطناعي</p>
        </div>
        
        {detectedSymptoms.length > 0 && (
          <motion.div 
            className="detected-symptoms-header"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <span className="symptoms-label">أعراض مكتشفة:</span>
            <div className="symptoms-tags">
              {detectedSymptoms.map((symptom, index) => (
                <span key={index} className="symptom-tag">
                  {symptom}
                </span>
              ))}
            </div>
          </motion.div>
        )}
      </div>
      
      {/* منطقة الرسائل */}
      <div className="chat-messages">
        <AnimatePresence>
          {messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={message}
              onCopy={handleCopyMessage}
              onFeedback={handleMessageFeedback}
            />
          ))}
        </AnimatePresence>
        
        {/* مؤشر الكتابة */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="typing-indicator"
          >
            <LoadingSpinner 
              type="brain" 
              size="small" 
              message="المساعد الطبي يحلل ويفكر..."
            />
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* منطقة الإدخال */}
      <div className="chat-input">
        <div className="input-container">
          {/* أزرار إضافية */}
          <div className="input-actions-left">
            <button 
              className="action-btn"
              onClick={() => fileInputRef.current?.click()}
              title="رفع ملف"
            >
              <Paperclip size={18} />
            </button>
            
            <button 
              className="action-btn"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              title="إضافة رمز تعبيري"
            >
              <Smile size={18} />
            </button>
          </div>

          {/* حقل النص */}
          <div className="input-wrapper">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="اكتب استفسارك الطبي بالتفصيل..."
              disabled={isLoading}
              rows={1}
              className="message-input"
            />
            
            <div className="input-counter">
              {inputValue.length}/{APP_CONFIG.limits.maxMessageLength}
            </div>
          </div>

          {/* أزرار الإرسال */}
          <div className="input-actions-right">
            <button 
              className={`voice-btn ${isRecording ? 'recording' : ''}`}
              onClick={handleVoiceRecording}
              title={isRecording ? 'إيقاف التسجيل' : 'تسجيل صوتي'}
            >
              {isRecording ? <MicOff size={18} /> : <Mic size={18} />}
            </button>
            
            <button 
              className="send-btn"
              onClick={handleSendMessage}
              disabled={isLoading || !inputValue.trim()}
              title="إرسال"
            >
              <Send size={18} />
            </button>
          </div>
        </div>

        {/* إدخال الملفات المخفي */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*,.pdf,.doc,.docx"
          onChange={handleFileUpload}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  );
};

export default ChatContainer;
