.chat-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
  max-width: 100%;
  animation: slideIn 0.3s ease-out;
}

.chat-message.user {
  flex-direction: row-reverse;
}

.chat-message.system {
  justify-content: center;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.chat-message.user .message-avatar {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.chat-message.bot .message-avatar {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.chat-message.emergency .message-avatar {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  color: white;
  animation: pulse 2s infinite;
}

.chat-message.system .message-avatar {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
}

.message-content-wrapper {
  flex: 1;
  max-width: calc(100% - 60px);
}

.chat-message.system .message-content-wrapper {
  max-width: 80%;
  text-align: center;
}

.message-content {
  background: white;
  padding: 16px 20px;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  line-height: 1.6;
  position: relative;
  word-wrap: break-word;
  transition: all 0.3s ease;
}

.chat-message.user .message-content {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-bottom-right-radius: 6px;
}

.chat-message.bot .message-content {
  border-bottom-left-radius: 6px;
  border: 1px solid #e9ecef;
}

.chat-message.emergency .message-content {
  border-left: 4px solid #dc3545;
  background: #fff5f5;
  border-bottom-left-radius: 6px;
}

.chat-message.system .message-content {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  font-style: italic;
  color: #6c757d;
}

.message-line {
  margin-bottom: 4px;
}

.message-line:last-child {
  margin-bottom: 0;
}

.confidence-score {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0,0,0,0.1);
  font-size: 12px;
  color: #666;
  opacity: 0.8;
}

.chat-message.user .confidence-score {
  border-top-color: rgba(255,255,255,0.3);
  color: rgba(255,255,255,0.8);
}

.detected-symptoms {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0,0,0,0.1);
}

.chat-message.user .detected-symptoms {
  border-top-color: rgba(255,255,255,0.3);
}

.detected-symptoms strong {
  display: block;
  margin-bottom: 8px;
  font-size: 13px;
  color: #495057;
}

.chat-message.user .detected-symptoms strong {
  color: rgba(255,255,255,0.9);
}

.symptoms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.symptom-tag {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.chat-message.user .symptom-tag {
  background: rgba(255,255,255,0.2);
  color: white;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  gap: 10px;
}

.chat-message.user .message-meta {
  flex-direction: row-reverse;
}

.message-timestamp {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #6c757d;
  opacity: 0.7;
}

.message-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chat-message:hover .message-actions {
  opacity: 1;
}

.action-button {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 6px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background: #f8f9fa;
  color: #495057;
  transform: scale(1.1);
}

.action-button.positive:hover {
  background: #d4edda;
  color: #28a745;
}

.action-button.negative:hover {
  background: #f8d7da;
  color: #dc3545;
}

/* تأثيرات الحركة */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* تجاوب */
@media (max-width: 768px) {
  .chat-message {
    gap: 10px;
    margin-bottom: 16px;
  }
  
  .message-avatar {
    width: 36px;
    height: 36px;
  }
  
  .message-content {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .message-content-wrapper {
    max-width: calc(100% - 50px);
  }
  
  .message-actions {
    opacity: 1; /* دائماً مرئية على الموبايل */
  }
}

/* وضع الظلام */
@media (prefers-color-scheme: dark) {
  .message-content {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  
  .chat-message.system .message-content {
    background: #1a202c;
    color: #a0aec0;
    border-color: #2d3748;
  }
  
  .confidence-score {
    color: #a0aec0;
    border-top-color: rgba(255,255,255,0.1);
  }
  
  .symptom-tag {
    background: #4a5568;
    color: #e2e8f0;
  }
  
  .action-button {
    color: #a0aec0;
  }
  
  .action-button:hover {
    background: #4a5568;
    color: #e2e8f0;
  }
}
