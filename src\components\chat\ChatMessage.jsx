import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>, <PERSON>, Co<PERSON>, ThumbsUp, ThumbsDown } from 'lucide-react';
import { motion } from 'framer-motion';
import { MESSAGE_TYPES } from '../../config/constants.js';
import './ChatMessage.css';

const ChatMessage = ({ 
  message, 
  onCopy, 
  onFeedback, 
  showTimestamp = true,
  showActions = true 
}) => {
  const isUser = message.type === MESSAGE_TYPES.USER;
  const isEmergency = message.isEmergency || message.type === MESSAGE_TYPES.EMERGENCY;
  const isSystem = message.type === MESSAGE_TYPES.SYSTEM;

  const getMessageIcon = () => {
    if (isUser) return <User size={20} />;
    if (isEmergency) return <AlertTriangle size={20} />;
    return <Bot size={20} />;
  };

  const getMessageClass = () => {
    let classes = ['chat-message', message.type];
    if (isEmergency) classes.push('emergency');
    if (isSystem) classes.push('system');
    return classes.join(' ');
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return date.toLocaleDateString('ar-EG');
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(message.content);
    if (onCopy) onCopy(message.id);
  };

  const handleFeedback = (type) => {
    if (onFeedback) onFeedback(message.id, type);
  };

  return (
    <motion.div
      className={getMessageClass()}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="message-avatar">
        {getMessageIcon()}
      </div>
      
      <div className="message-content-wrapper">
        <div className="message-content">
          {message.content.split('\n').map((line, index) => (
            <div key={index} className="message-line">
              {line}
            </div>
          ))}
          
          {/* عرض درجة الثقة للرسائل الطبية */}
          {message.confidence && !isUser && (
            <div className="confidence-score">
              <Star size={12} />
              <span>دقة: {Math.round(message.confidence * 100)}%</span>
            </div>
          )}
          
          {/* عرض الأعراض المكتشفة */}
          {message.detectedSymptoms && message.detectedSymptoms.length > 0 && (
            <div className="detected-symptoms">
              <strong>أعراض مكتشفة:</strong>
              <div className="symptoms-list">
                {message.detectedSymptoms.map((symptom, index) => (
                  <span key={index} className="symptom-tag">
                    {symptom}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* معلومات إضافية */}
        <div className="message-meta">
          {showTimestamp && (
            <div className="message-timestamp">
              <Clock size={12} />
              <span>{formatTimestamp(message.timestamp)}</span>
            </div>
          )}
          
          {/* إجراءات الرسالة */}
          {showActions && !isUser && (
            <div className="message-actions">
              <button 
                className="action-button"
                onClick={handleCopy}
                title="نسخ النص"
              >
                <Copy size={14} />
              </button>
              
              <button 
                className="action-button positive"
                onClick={() => handleFeedback('positive')}
                title="مفيد"
              >
                <ThumbsUp size={14} />
              </button>
              
              <button 
                className="action-button negative"
                onClick={() => handleFeedback('negative')}
                title="غير مفيد"
              >
                <ThumbsDown size={14} />
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ChatMessage;
