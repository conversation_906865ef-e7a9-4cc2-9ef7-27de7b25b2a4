import React from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // تسجيل الخطأ (يمكن إرساله لخدمة مراقبة)
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // إرسال تقرير الخطأ (اختياري)
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <div className="error-container">
            <div className="error-icon">
              <AlertTriangle size={64} color="#dc3545" />
            </div>
            
            <div className="error-content">
              <h1 className="error-title">
                عذراً، حدث خطأ غير متوقع
              </h1>
              
              <p className="error-description">
                واجه التطبيق مشكلة تقنية. نعتذر عن الإزعاج.
              </p>
              
              {process.env.NODE_ENV === 'development' && (
                <details className="error-details">
                  <summary>تفاصيل الخطأ (للمطورين)</summary>
                  <pre className="error-stack">
                    {this.state.error && this.state.error.toString()}
                    <br />
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}
              
              <div className="error-actions">
                <button 
                  className="error-button primary"
                  onClick={this.handleReload}
                >
                  <RefreshCw size={16} />
                  إعادة تحميل الصفحة
                </button>
                
                <button 
                  className="error-button secondary"
                  onClick={this.handleReset}
                >
                  <Home size={16} />
                  المحاولة مرة أخرى
                </button>
              </div>
              
              <div className="error-help">
                <p>إذا استمرت المشكلة:</p>
                <ul>
                  <li>تأكد من اتصالك بالإنترنت</li>
                  <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                  <li>جرب متصفح آخر</li>
                  <li>تواصل مع الدعم التقني</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
