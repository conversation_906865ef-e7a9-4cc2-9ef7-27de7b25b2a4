.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.spinner-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.spinner-icon-wrapper {
  position: relative;
  animation: pulse 2s ease-in-out infinite;
}

.spinner-icon {
  color: #4facfe;
  filter: drop-shadow(0 0 10px rgba(79, 172, 254, 0.3));
}

.spinner-small .spinner-icon {
  width: 24px;
  height: 24px;
}

.spinner-medium .spinner-icon {
  width: 32px;
  height: 32px;
}

.spinner-large .spinner-icon {
  width: 48px;
  height: 48px;
}

.spinner-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.spinner-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.spinner-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.spinner-dot:nth-child(3) {
  animation-delay: 0s;
}

.spinner-message {
  font-size: 14px;
  color: #666;
  text-align: center;
  font-weight: 500;
  opacity: 0.8;
}

.spinner-small .spinner-message {
  font-size: 12px;
}

.spinner-large .spinner-message {
  font-size: 16px;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* تأثيرات إضافية للأنواع المختلفة */
.spinner-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border: 2px solid transparent;
  border-top: 2px solid #4facfe;
  border-radius: 50%;
  animation: spin 2s linear infinite;
  opacity: 0.3;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* حالات خاصة */
.loading-spinner.thinking .spinner-icon {
  color: #28a745;
}

.loading-spinner.error .spinner-icon {
  color: #dc3545;
}

.loading-spinner.warning .spinner-icon {
  color: #ffc107;
}

/* تجاوب */
@media (max-width: 768px) {
  .spinner-large .spinner-icon {
    width: 40px;
    height: 40px;
  }
  
  .spinner-medium .spinner-icon {
    width: 28px;
    height: 28px;
  }
  
  .spinner-message {
    font-size: 13px;
  }
}
