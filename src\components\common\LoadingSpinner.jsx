import React from 'react';
import { Brain, Heart, Activity } from 'lucide-react';
import './LoadingSpinner.css';

const LoadingSpinner = ({ 
  size = 'medium', 
  type = 'brain', 
  message = 'جاري التحميل...', 
  showMessage = true 
}) => {
  const sizeClasses = {
    small: 'spinner-small',
    medium: 'spinner-medium',
    large: 'spinner-large'
  };

  const getIcon = () => {
    switch (type) {
      case 'brain':
        return <Brain className="spinner-icon" />;
      case 'heart':
        return <Heart className="spinner-icon" />;
      case 'activity':
        return <Activity className="spinner-icon" />;
      default:
        return <Brain className="spinner-icon" />;
    }
  };

  return (
    <div className={`loading-spinner ${sizeClasses[size]}`}>
      <div className="spinner-container">
        <div className="spinner-icon-wrapper">
          {getIcon()}
        </div>
        <div className="spinner-dots">
          <div className="spinner-dot"></div>
          <div className="spinner-dot"></div>
          <div className="spinner-dot"></div>
        </div>
      </div>
      {showMessage && (
        <div className="spinner-message">
          {message}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
