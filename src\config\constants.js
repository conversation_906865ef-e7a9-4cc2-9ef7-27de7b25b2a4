// تكوين ثوابت التطبيق
export const APP_CONFIG = {
  name: 'المساعد الطبي الذكي',
  version: '2.0.0',
  description: 'نظام ذكي متكامل للاستشارات الطبية وتحليل الأعراض',
  author: 'Medical AI Team',
  
  // URLs
  api: {
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000',
    timeout: 30000,
    retries: 3
  },
  
  // الميزات
  features: {
    aiConsultation: true,
    symptomAnalysis: true,
    labInterpretation: true,
    emergencyDetection: true,
    firstAid: true,
    healthTips: true,
    voiceInput: false, // قيد التطوير
    multilingual: true
  },
  
  // اللغات المدعومة
  languages: {
    ar: { name: 'العربية', dir: 'rtl', flag: '🇪🇬' },
    en: { name: 'English', dir: 'ltr', flag: '🇺🇸' }
  },
  
  // أرقام الطوارئ
  emergency: {
    ambulance: '123',
    police: '122',
    fire: '180',
    poison: '16000'
  },
  
  // حدود النظام
  limits: {
    maxMessageLength: 2000,
    maxHistoryItems: 100,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    sessionTimeout: 30 * 60 * 1000 // 30 دقيقة
  }
};

// رسائل النظام
export const MESSAGES = {
  ar: {
    welcome: 'مرحباً! أنا المساعد الطبي الذكي المتطور 🩺\n\nأقدر أساعدك في:\n• الاستشارات الطبية المتخصصة\n• تحليل الأعراض والتشخيص المبدئي\n• تفسير نتائج التحاليل الطبية\n• الإسعافات الأولية والطوارئ\n• النصائح الغذائية والصحية\n\nإزاي أقدر أساعدك النهاردة؟',
    thinking: 'المساعد الطبي يحلل ويفكر...',
    error: 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.',
    networkError: 'مشكلة في الاتصال. تحقق من الإنترنت.',
    disclaimer: '⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.'
  },
  en: {
    welcome: 'Welcome! I am your Advanced Smart Medical Assistant 🩺\n\nI can help you with:\n• Specialized medical consultations\n• Symptom analysis and preliminary diagnosis\n• Medical test interpretation\n• First aid and emergencies\n• Nutritional and health advice\n\nHow can I help you today?',
    thinking: 'Medical assistant is analyzing and thinking...',
    error: 'Sorry, an error occurred. Please try again.',
    networkError: 'Connection problem. Check your internet.',
    disclaimer: '⚠️ This is not a medical diagnosis. If symptoms persist or worsen, please consult a specialist doctor.'
  }
};

// أنواع الرسائل
export const MESSAGE_TYPES = {
  WELCOME: 'welcome',
  USER: 'user',
  BOT: 'bot',
  SYSTEM: 'system',
  ERROR: 'error',
  EMERGENCY: 'emergency',
  FIRST_AID: 'first_aid',
  LAB_RESULT: 'lab_result',
  SYMPTOM_ANALYSIS: 'symptom_analysis'
};

// حالات التطبيق
export const APP_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  THINKING: 'thinking',
  ERROR: 'error',
  OFFLINE: 'offline'
};

// مستويات الطوارئ
export const EMERGENCY_LEVELS = {
  LOW: { level: 1, color: '#28a745', label: 'منخفض' },
  MEDIUM: { level: 2, color: '#ffc107', label: 'متوسط' },
  HIGH: { level: 3, color: '#fd7e14', label: 'عالي' },
  CRITICAL: { level: 4, color: '#dc3545', label: 'حرج' }
};

export default APP_CONFIG;
