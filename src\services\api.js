import axios from 'axios';
import { APP_CONFIG } from '../config/constants.js';

// إنشاء instance للـ API
const api = axios.create({
  baseURL: APP_CONFIG.api.baseURL,
  timeout: APP_CONFIG.api.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Interceptors للطلبات
api.interceptors.request.use(
  (config) => {
    // إضافة timestamp لكل طلب
    config.metadata = { startTime: new Date() };
    
    // إضافة user agent مخصص
    config.headers['X-Client-Version'] = APP_CONFIG.version;
    
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Interceptors للاستجابات
api.interceptors.response.use(
  (response) => {
    const duration = new Date() - response.config.metadata.startTime;
    console.log(`✅ API Response: ${response.status} (${duration}ms)`);
    return response;
  },
  (error) => {
    const duration = error.config?.metadata ? 
      new Date() - error.config.metadata.startTime : 0;
    
    console.error(`❌ API Error: ${error.response?.status || 'Network'} (${duration}ms)`, error);
    
    // معالجة أخطاء شائعة
    if (error.code === 'ECONNABORTED') {
      error.message = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
    } else if (error.code === 'ERR_NETWORK') {
      error.message = 'مشكلة في الشبكة. تحقق من الاتصال بالإنترنت.';
    } else if (error.response?.status === 500) {
      error.message = 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
    }
    
    return Promise.reject(error);
  }
);

// خدمات API
export const medicalAPI = {
  // إرسال رسالة للمساعد الطبي
  async sendMessage(message, userId = null) {
    try {
      const response = await api.post('/api/chat', {
        message: message.trim(),
        user_id: userId || `user_${Date.now()}`,
        timestamp: new Date().toISOString()
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في إرسال الرسالة'
      };
    }
  },

  // تحليل الأعراض
  async analyzeSymptoms(symptoms) {
    try {
      const response = await api.post('/api/analyze-symptoms', {
        symptoms: symptoms.trim()
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في تحليل الأعراض'
      };
    }
  },

  // تفسير التحاليل
  async interpretLabs(labResults) {
    try {
      const response = await api.post('/api/interpret-labs', {
        lab_results: labResults.trim()
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في تفسير التحاليل'
      };
    }
  },

  // فحص الطوارئ
  async checkEmergency(message) {
    try {
      const response = await api.post('/api/emergency-check', {
        message: message.trim()
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في فحص الطوارئ'
      };
    }
  },

  // الحصول على نصائح صحية
  async getHealthTips() {
    try {
      const response = await api.get('/api/health-tips');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في جلب النصائح الصحية'
      };
    }
  },

  // الحصول على تاريخ المحادثات
  async getHistory() {
    try {
      const response = await api.get('/api/history');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في جلب التاريخ'
      };
    }
  },

  // الحصول على دليل الإسعافات الأولية
  async getFirstAidGuides() {
    try {
      const response = await api.get('/api/first-aid');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في جلب دليل الإسعافات'
      };
    }
  },

  // فحص حالة الخادم
  async checkHealth() {
    try {
      const response = await api.get('/api/health', { timeout: 5000 });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: 'الخادم غير متاح'
      };
    }
  }
};

// إعادة المحاولة التلقائية
export const retryRequest = async (requestFn, maxRetries = APP_CONFIG.api.retries) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await requestFn();
      if (result.success) return result;
      
      if (i === maxRetries - 1) return result;
      
      // انتظار متزايد بين المحاولات
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
    } catch (error) {
      if (i === maxRetries - 1) {
        return {
          success: false,
          error: error.message || 'فشل بعد عدة محاولات'
        };
      }
      
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
    }
  }
};

export default api;
