import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { APP_CONFIG, MESSAGE_TYPES, APP_STATES } from '../config/constants.js';

// Store رئيسي للتطبيق
export const useAppStore = create(
  persist(
    (set, get) => ({
      // حالة التطبيق
      appState: APP_STATES.IDLE,
      language: 'ar',
      theme: 'light',
      
      // بيانات المستخدم
      user: {
        id: null,
        preferences: {
          notifications: true,
          soundEffects: true,
          autoSave: true
        }
      },
      
      // الرسائل والمحادثات
      messages: [
        {
          id: 1,
          type: MESSAGE_TYPES.WELCOME,
          content: APP_CONFIG.languages.ar.name === 'العربية' ? 
            'مرحباً! أنا المساعد الطبي الذكي المتطور 🩺' : 
            'Welcome! I am your Advanced Smart Medical Assistant 🩺',
          timestamp: new Date(),
          sender: 'bot'
        }
      ],
      currentTab: 'chat',
      isLoading: false,
      
      // البيانات الطبية
      detectedSymptoms: [],
      healthTip: '',
      conversationHistory: [],
      emergencyStatus: null,
      
      // الإحصائيات
      stats: {
        totalQuestions: 0,
        emergenciesDetected: 0,
        symptomsAnalyzed: 0,
        labsInterpreted: 0
      },
      
      // Actions
      setAppState: (state) => set({ appState: state }),
      
      setLanguage: (language) => set({ language }),
      
      setTheme: (theme) => set({ theme }),
      
      setCurrentTab: (tab) => set({ currentTab: tab }),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      // إضافة رسالة جديدة
      addMessage: (message) => set((state) => {
        const newMessage = {
          id: Date.now(),
          timestamp: new Date(),
          ...message
        };
        
        return {
          messages: [...state.messages, newMessage]
        };
      }),
      
      // مسح الرسائل
      clearMessages: () => set({
        messages: [
          {
            id: 1,
            type: MESSAGE_TYPES.WELCOME,
            content: 'مرحباً! أنا المساعد الطبي الذكي المتطور 🩺',
            timestamp: new Date(),
            sender: 'bot'
          }
        ]
      }),
      
      // تحديث الأعراض المكتشفة
      setDetectedSymptoms: (symptoms) => set({ detectedSymptoms: symptoms }),
      
      // إضافة أعراض جديدة
      addDetectedSymptoms: (newSymptoms) => set((state) => ({
        detectedSymptoms: [...new Set([...state.detectedSymptoms, ...newSymptoms])]
      })),
      
      // تحديث النصيحة الصحية
      setHealthTip: (tip) => set({ healthTip: tip }),
      
      // إضافة محادثة للتاريخ
      addToHistory: (conversation) => set((state) => ({
        conversationHistory: [
          ...state.conversationHistory,
          {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            ...conversation
          }
        ].slice(-APP_CONFIG.limits.maxHistoryItems) // الاحتفاظ بآخر 100 محادثة
      })),
      
      // تحديث حالة الطوارئ
      setEmergencyStatus: (status) => set({ emergencyStatus: status }),
      
      // تحديث الإحصائيات
      updateStats: (statType) => set((state) => ({
        stats: {
          ...state.stats,
          [statType]: state.stats[statType] + 1
        }
      })),
      
      // تحديث تفضيلات المستخدم
      updateUserPreferences: (preferences) => set((state) => ({
        user: {
          ...state.user,
          preferences: {
            ...state.user.preferences,
            ...preferences
          }
        }
      })),
      
      // إعادة تعيين التطبيق
      resetApp: () => set({
        messages: [
          {
            id: 1,
            type: MESSAGE_TYPES.WELCOME,
            content: 'مرحباً! أنا المساعد الطبي الذكي المتطور 🩺',
            timestamp: new Date(),
            sender: 'bot'
          }
        ],
        detectedSymptoms: [],
        conversationHistory: [],
        emergencyStatus: null,
        currentTab: 'chat',
        appState: APP_STATES.IDLE
      }),
      
      // الحصول على آخر رسالة
      getLastMessage: () => {
        const state = get();
        return state.messages[state.messages.length - 1];
      },
      
      // الحصول على رسائل اليوم
      getTodayMessages: () => {
        const state = get();
        const today = new Date().toDateString();
        return state.messages.filter(msg => 
          new Date(msg.timestamp).toDateString() === today
        );
      },
      
      // البحث في الرسائل
      searchMessages: (query) => {
        const state = get();
        return state.messages.filter(msg =>
          msg.content.toLowerCase().includes(query.toLowerCase())
        );
      }
    }),
    {
      name: 'medical-assistant-store',
      partialize: (state) => ({
        language: state.language,
        theme: state.theme,
        user: state.user,
        conversationHistory: state.conversationHistory,
        stats: state.stats
      })
    }
  )
);

// Store للإعدادات
export const useSettingsStore = create((set) => ({
  notifications: {
    enabled: true,
    sound: true,
    desktop: true,
    emergency: true
  },
  
  accessibility: {
    fontSize: 'medium',
    highContrast: false,
    screenReader: false,
    keyboardNavigation: true
  },
  
  privacy: {
    saveHistory: true,
    analytics: false,
    crashReports: true
  },
  
  updateNotifications: (settings) => set((state) => ({
    notifications: { ...state.notifications, ...settings }
  })),
  
  updateAccessibility: (settings) => set((state) => ({
    accessibility: { ...state.accessibility, ...settings }
  })),
  
  updatePrivacy: (settings) => set((state) => ({
    privacy: { ...state.privacy, ...settings }
  }))
}));

export default useAppStore;
