import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from '../App.jsx';

// Mock للمكونات الخارجية
vi.mock('../components/chat/ChatContainer.jsx', () => ({
  default: () => <div data-testid="chat-container">Chat Container</div>
}));

vi.mock('../services/api.js', () => ({
  medicalAPI: {
    getHealthTips: vi.fn().mockResolvedValue({
      success: true,
      data: { daily_tip: 'اشرب 8 أكواب مياه يومياً' }
    })
  }
}));

describe('App Component', () => {
  it('يجب أن يعرض عنوان التطبيق', () => {
    render(<App />);
    
    const title = screen.getByText(/المساعد الطبي الذكي/i);
    expect(title).toBeInTheDocument();
  });

  it('يجب أن يعرض التبويبات الأربعة', () => {
    render(<App />);
    
    expect(screen.getByText('استشارة طبية')).toBeInTheDocument();
    expect(screen.getByText('تحليل الأعراض')).toBeInTheDocument();
    expect(screen.getByText('تفسير التحاليل')).toBeInTheDocument();
    expect(screen.getByText('طوارئ')).toBeInTheDocument();
  });

  it('يجب أن يعرض زر تغيير اللغة', () => {
    render(<App />);
    
    const languageButton = screen.getByText('English');
    expect(languageButton).toBeInTheDocument();
  });

  it('يجب أن يعرض التذييل مع التحذير الطبي', () => {
    render(<App />);
    
    const disclaimer = screen.getByText(/هذا المساعد يقدم معلومات طبية عامة فقط/i);
    expect(disclaimer).toBeInTheDocument();
  });

  it('يجب أن يعرض ChatContainer بشكل افتراضي', () => {
    render(<App />);
    
    const chatContainer = screen.getByTestId('chat-container');
    expect(chatContainer).toBeInTheDocument();
  });
});
