@echo off
echo 🩺 المساعد الطبي الذكي
echo ==========================================
echo.

echo 📦 تثبيت مكتبات Frontend...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت مكتبات Frontend
    pause
    exit /b 1
)

echo.
echo 🐍 تثبيت مكتبات Python...
cd backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت مكتبات Python
    pause
    exit /b 1
)
cd ..

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo.
echo 🚀 لتشغيل التطبيق:
echo 1. شغل Frontend: npm run dev
echo 2. شغل Backend: python backend/app.py
echo.
echo 🌐 ثم افتح المتصفح على: http://localhost:3000
echo.
pause
