@echo off
title 🩺 المساعد الطبي الذكي المتطور v2.0 - Smart Medical Assistant
color 0B
chcp 65001 >nul

:: تعيين متغيرات
set "PROJECT_NAME=المساعد الطبي الذكي المتطور"
set "VERSION=v2.0.0"
set "FRONTEND_PORT=3000"
set "BACKEND_PORT=5000"

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          🩺 %PROJECT_NAME% %VERSION%                          ║
echo ║                        Advanced Smart Medical Assistant                        ║
echo ║                                                                              ║
echo ║                    🤖 مدعوم بالذكاء الاصطناعي المتقدم                        ║
echo ║                      Powered by Advanced AI Technology                        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌟 مرحباً بك في أحدث نظام طبي ذكي متكامل
echo 🌟 Welcome to the most advanced integrated medical system
echo.
echo ⚡ الميزات المتاحة | Available Features:
echo   • 🧠 استشارة طبية ذكية مدعومة بـ Google Gemini AI
echo   • 🔍 تحليل الأعراض المتقدم مع كشف الطوارئ
echo   • 📊 تفسير التحاليل الطبية بطريقة مبسطة
echo   • 🚨 دليل الإسعافات الأولية التفاعلي
echo   • 💊 نصائح صحية يومية مخصصة
echo   • 📱 واجهة متجاوبة تعمل على جميع الأجهزة
echo.

:: فحص المتطلبات
echo 📋 فحص المتطلبات الأساسية...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

:: فحص Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js %NODE_VERSION% متوفر
)

:: فحص Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تحميل وتثبيت Python من: https://python.org/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo ✅ Python %PYTHON_VERSION% متوفر
)

:: فحص npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm %NPM_VERSION% متوفر
)

echo.
echo 📦 تثبيت وتحديث المكتبات...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo 🔧 تثبيت مكتبات Frontend...
call npm install --silent
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت مكتبات Frontend
    echo 💡 جرب تشغيل: npm install --force
    pause
    exit /b 1
)
echo ✅ تم تثبيت مكتبات Frontend بنجاح

echo.
echo 🐍 تثبيت مكتبات Python...
pip install flask flask-cors google-genai --quiet --disable-pip-version-check
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: بعض مكتبات Python قد لا تكون متاحة
    echo 💡 سيتم استخدام النسخة المبسطة من Backend
) else (
    echo ✅ تم تثبيت مكتبات Python بنجاح
)

echo.
echo 🚀 تشغيل النظام...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo 🌐 تشغيل Frontend Server على المنفذ %FRONTEND_PORT%...
start "🎨 Frontend - React App" cmd /k "title 🎨 Frontend Server - Port %FRONTEND_PORT% && echo. && echo ╔══════════════════════════════════════════════════════════════╗ && echo ║                    🎨 Frontend Server                        ║ && echo ║                   React + Vite + TypeScript                   ║ && echo ╚══════════════════════════════════════════════════════════════╝ && echo. && echo 🚀 Starting development server... && echo. && npm run dev"

:: انتظار قصير
timeout /t 3 /nobreak >nul

echo 🐍 تشغيل Backend Server على المنفذ %BACKEND_PORT%...
start "🔧 Backend - Python API" cmd /k "title 🔧 Backend Server - Port %BACKEND_PORT% && echo. && echo ╔══════════════════════════════════════════════════════════════╗ && echo ║                     🔧 Backend Server                        ║ && echo ║                   Python Flask + AI Integration               ║ && echo ╚══════════════════════════════════════════════════════════════╝ && echo. && echo 🚀 Starting API server... && echo. && python backend\simple_app.py"

echo.
echo ⏳ انتظار تشغيل الخوادم...
echo    Waiting for servers to start...

:: انتظار أطول للتأكد من تشغيل الخوادم
timeout /t 8 /nobreak >nul

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           ✅ تم تشغيل النظام بنجاح!                           ║
echo ║                         🎉 System Started Successfully!                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌐 الروابط المتاحة | Available Links:
echo ┌──────────────────────────────────────────────────────────────────────────────┐
echo │  🎨 Frontend Application:    http://localhost:%FRONTEND_PORT%                           │
echo │  🔧 Backend API:             http://localhost:%BACKEND_PORT%                            │
echo │  📊 Health Tips API:         http://localhost:%BACKEND_PORT%/api/health-tips            │
echo │  🚨 First Aid API:           http://localhost:%BACKEND_PORT%/api/first-aid              │
echo │  📋 Chat API:                http://localhost:%BACKEND_PORT%/api/chat                   │
echo └──────────────────────────────────────────────────────────────────────────────┘
echo.
echo 🎯 الميزات الجاهزة للاستخدام | Ready Features:
echo   ✨ Smart Medical Consultation with AI
echo   🔍 Advanced Symptoms Analysis
echo   📊 Medical Lab Results Interpretation  
echo   🚨 Interactive First Aid Guide
echo   💊 Daily Health Tips
echo   📱 Responsive Design for All Devices
echo.
echo 💡 نصائح مهمة | Important Tips:
echo   • اتركي نوافذ الخوادم مفتوحة للحفاظ على عمل النظام
echo   • Keep server windows open to maintain system operation
echo   • استخدم Ctrl+C لإيقاف أي خادم | Use Ctrl+C to stop any server
echo   • في حالة وجود مشاكل، راجع ملف README.md
echo   • For troubleshooting, check README.md file
echo.
echo 🔗 للمزيد من المعلومات | For More Information:
echo   📚 Documentation: README.md
echo   🐛 Report Issues: GitHub Issues
echo   💬 Discussions: GitHub Discussions
echo   📧 Contact: <EMAIL>
echo.

:: اختبار سريع للخوادم
echo 🧪 اختبار سريع للنظام...
timeout /t 2 /nobreak >nul

curl -s http://localhost:%FRONTEND_PORT% >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend Server يستجيب بشكل صحيح
) else (
    echo ⚠️  Frontend Server قد يحتاج وقت إضافي للتشغيل
)

curl -s http://localhost:%BACKEND_PORT%/api/health-tips >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend API يستجيب بشكل صحيح
) else (
    echo ⚠️  Backend API قد يحتاج وقت إضافي للتشغيل
)

echo.
echo 🌟 هل تريد فتح التطبيق في المتصفح؟
echo 🌟 Would you like to open the application in browser?
echo.
echo اضغط أي مفتاح لفتح التطبيق، أو Ctrl+C للخروج...
echo Press any key to open the app, or Ctrl+C to exit...
pause >nul

echo.
echo 🚀 فتح التطبيق في المتصفح...
echo 🚀 Opening application in browser...

start http://localhost:%FRONTEND_PORT%

echo.
echo ✅ تم فتح التطبيق بنجاح!
echo ✅ Application opened successfully!
echo.
echo 🎉 استمتع باستخدام المساعد الطبي الذكي!
echo 🎉 Enjoy using the Smart Medical Assistant!
echo.
echo اضغط أي مفتاح للخروج من هذه النافذة...
echo Press any key to exit this window...
pause >nul
