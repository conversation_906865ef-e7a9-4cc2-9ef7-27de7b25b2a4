@echo off
title اختبار النظام - System Test
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧪 اختبار النظام الطبي                        ║
echo ║                      System Health Check                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص حالة الخوادم...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo 🌐 اختبار Frontend Server...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend Server يعمل بشكل صحيح
) else (
    echo ❌ Frontend Server غير متاح
)

echo.
echo 🔧 اختبار Backend API...
curl -s http://localhost:5000/api/health-tips >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend API يعمل بشكل صحيح
) else (
    echo ❌ Backend API غير متاح
)

echo.
echo 📊 اختبار API Endpoints...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

curl -s -X GET http://localhost:5000/api/health-tips >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/health-tips
) else (
    echo ❌ /api/health-tips
)

curl -s -X GET http://localhost:5000/api/first-aid >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/first-aid
) else (
    echo ❌ /api/first-aid
)

curl -s -X GET http://localhost:5000/api/history >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/history
) else (
    echo ❌ /api/history
)

echo.
echo 🧪 اختبار Frontend Tests...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

npm test -- --run >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ جميع الاختبارات نجحت
) else (
    echo ⚠️  بعض الاختبارات فشلت أو غير متاحة
)

echo.
echo 📱 فحص الملفات الأساسية...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

if exist "src\App.jsx" (
    echo ✅ src\App.jsx
) else (
    echo ❌ src\App.jsx مفقود
)

if exist "src\components\chat\ChatContainer.jsx" (
    echo ✅ ChatContainer.jsx
) else (
    echo ❌ ChatContainer.jsx مفقود
)

if exist "backend\simple_app.py" (
    echo ✅ backend\simple_app.py
) else (
    echo ❌ backend\simple_app.py مفقود
)

if exist "package.json" (
    echo ✅ package.json
) else (
    echo ❌ package.json مفقود
)

echo.
echo 🌐 الروابط المتاحة:
echo ┌─────────────────────────────────────────────────────────────┐
echo │  🎨 Frontend:     http://localhost:3000                     │
echo │  🔧 Backend:      http://localhost:5000                     │
echo │  📊 Health Tips:  http://localhost:5000/api/health-tips     │
echo │  🚨 First Aid:    http://localhost:5000/api/first-aid       │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo 💡 لفتح التطبيق في المتصفح، اضغط أي مفتاح...
pause >nul

start http://localhost:3000

echo.
echo ✅ تم فتح التطبيق في المتصفح!
echo.
pause
